import { useUserProfile } from '@/common/profile';
import { withUserProfile } from '@/common/withUserProfile';
import FullScreenActivityIndicator from '@/components/FullScreenActivityIndicator';
import { JsStack } from '@/components/global/JsStack';
import { ModalProvider } from '@/components/global/modal';
import { RevenueCatProvider } from '@/hooks/usePurchases';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';

function Layout() {
  const theme = useThemeColorLegacy();
  const { profile } = useUserProfile();

  if (!profile) return <FullScreenActivityIndicator />;

  return (
    <RevenueCatProvider>
      <ModalProvider>
        <JsStack initialRouteName="app" screenOptions={{ headerShown: false }}>
          <JsStack.Screen name="onboarding" options={{ presentation: 'modal' }} />
          <JsStack.Screen name="settings" options={{ presentation: 'modal' }} />
          <JsStack.Screen name="company" options={{ headerShown: false }} />
        </JsStack>
      </ModalProvider>
    </RevenueCatProvider>
  );
}

export default withUserProfile(Layout);
