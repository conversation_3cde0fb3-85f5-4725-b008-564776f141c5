import React, { createContext, useContext, useEffect, useState } from 'react';

import * as SecureStore from 'expo-secure-store';
import { useColorScheme } from 'react-native';

import { ThemedView } from '@/components/global/ThemedView';
import { AppTheme } from '@/constants/Colors';
import { ThemeContextType, ThemeType } from '@/src/types';

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_KEY = 'user_theme_preference';

// Default themes based on system preference
const DEFAULT_LIGHT_THEME: AppTheme = 'lightBlue';
const DEFAULT_DARK_THEME: AppTheme = 'darkBlue';

export function ThemeProviderContext({ children }: { children: React.ReactNode }) {
  const systemColorScheme = useColorScheme() ?? 'light';

  // Initialize theme with system-based default
  const [theme, setThemeState] = useState<ThemeType>(() => {
    // On first load, default to system preference with appropriate theme
    return systemColorScheme === 'light' ? DEFAULT_LIGHT_THEME : DEFAULT_DARK_THEME;
  });

  const [isInitialized, setIsInitialized] = useState(false);

  // Load saved theme from SecureStore on mount
  useEffect(() => {
    const loadSavedTheme = async () => {
      try {
        const savedTheme = await SecureStore.getItemAsync(THEME_KEY);
        if (savedTheme) {
          // Validate that the saved theme is a valid theme
          const validThemes: ThemeType[] = [
            'system',
            'lightOrange',
            'lightRed',
            'lightPurple',
            'lightBlue',
            'lightGreen',
            'darkOrange',
            'darkRed',
            'darkPurple',
            'darkBlue',
            'darkGreen',
          ];

          if (validThemes.includes(savedTheme as ThemeType)) {
            setThemeState(savedTheme as ThemeType);
          }
        }
      } catch (error) {
        console.warn('Failed to load theme from SecureStore:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    loadSavedTheme();
  }, []);

  // Save theme changes to SecureStore whenever theme changes (but not on initial load)
  useEffect(() => {
    if (!isInitialized) return;

    const saveTheme = async () => {
      try {
        await SecureStore.setItemAsync(THEME_KEY, theme);
      } catch (error) {
        console.warn('Failed to save theme to SecureStore:', error);
      }
    };

    saveTheme();
  }, [theme, isInitialized]);

  const setTheme = (newTheme: ThemeType) => {
    setThemeState(newTheme);
  };

  // Determine the current theme based on user preference and system theme
  const currentTheme: AppTheme = (() => {
    if (theme === 'system') {
      // If user chose system, use default themes based on system preference
      return systemColorScheme === 'light' ? DEFAULT_LIGHT_THEME : DEFAULT_DARK_THEME;
    }
    // Otherwise use the specific theme chosen by user
    return theme as AppTheme;
  })();

  // Provide a stable context value even during initialization
  const contextValue = {
    theme,
    setTheme,
    currentTheme,
  };

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>;
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    console.error('useTheme must be used within a ThemeProviderContext');
    // Return a default theme instead of throwing to prevent crashes
    return {
      theme: 'system' as ThemeType,
      setTheme: () => {},
      currentTheme: DEFAULT_DARK_THEME,
    };
  }
  // Ensure currentTheme is always defined
  return {
    ...context,
    currentTheme: context.currentTheme || DEFAULT_DARK_THEME,
  };
}
